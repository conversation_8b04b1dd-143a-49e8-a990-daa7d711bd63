import * as vscode from 'vscode';
import { ChatService } from '../services/chatService';
import { ChatWebviewTemplate } from '../templates/chatWebview';
import { WebviewMessage, WebviewMessageType, MessageType } from '../types/chat';
import { Logger } from '../utils/logger';

/**
 * 聊天视图提供器
 * 实现 VSCode WebviewViewProvider 接口，管理聊天界面
 */
export class ChatViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'ai-chat-view';
    
    private _view?: vscode.WebviewView;
    private chatService: ChatService;

    constructor(chatService: ChatService) {
        this.chatService = chatService;
        Logger.info('ChatViewProvider 初始化完成');
    }

    /**
     * 解析 Webview 视图
     * @param webviewView Webview 视图
     * @param context 解析上下文
     * @param _token 取消令牌
     */
    resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        Logger.methodCall('ChatViewProvider', 'resolveWebviewView');
        
        this._view = webviewView;

        // 配置 Webview 选项
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.joinPath(vscode.workspace.workspaceFolders![0].uri, 'media')
            ]
        };

        // 设置 HTML 内容
        webviewView.webview.html = ChatWebviewTemplate.getHtml(webviewView.webview);

        // 监听来自 Webview 的消息
        this.setupMessageHandling(webviewView);

        // 设置视图标题和描述
        webviewView.title = 'AI 助手';
        webviewView.description = '智能编程助手';

        Logger.success('聊天视图已初始化');
    }

    /**
     * 设置消息处理
     * @param webviewView Webview 视图
     */
    private setupMessageHandling(webviewView: vscode.WebviewView): void {
        Logger.methodCall('ChatViewProvider', 'setupMessageHandling');

        webviewView.webview.onDidReceiveMessage(
            async (message: WebviewMessage) => {
                try {
                    await this.handleWebviewMessage(message);
                } catch (error) {
                    Logger.error('处理 Webview 消息时出错', error as Error);
                    this.sendErrorToWebview('处理消息时出现错误，请重试');
                }
            }
        );
    }

    /**
     * 处理来自 Webview 的消息
     * @param message Webview 消息
     */
    private async handleWebviewMessage(message: WebviewMessage): Promise<void> {
        Logger.debug(`收到 Webview 消息: ${message.type}`, message.data);

        switch (message.type) {
            case WebviewMessageType.READY:
                await this.handleReady();
                break;

            case WebviewMessageType.SEND_MESSAGE:
                await this.handleSendMessage(message.data?.content);
                break;

            case WebviewMessageType.CLEAR_CHAT:
                await this.handleClearChat();
                break;

            case WebviewMessageType.EXPORT_CHAT:
                await this.handleExportChat();
                break;

            case WebviewMessageType.LOAD_HISTORY:
                await this.handleLoadHistory();
                break;

            case WebviewMessageType.UPDATE_CONFIG:
                await this.handleUpdateConfig(message.data);
                break;

            default:
                Logger.warn(`未知的 Webview 消息类型: ${message.type}`);
        }
    }

    /**
     * 处理 Webview 就绪事件
     */
    private async handleReady(): Promise<void> {
        Logger.debug('Webview 已就绪');
        
        // 加载当前会话的消息
        const session = this.chatService.getCurrentSession();
        if (session.messages.length > 0) {
            for (const message of session.messages) {
                this.sendMessageToWebview(message);
            }
        }
    }

    /**
     * 处理发送消息
     * @param content 消息内容
     */
    private async handleSendMessage(content: string): Promise<void> {
        if (!content || !content.trim()) {
            this.sendErrorToWebview('消息内容不能为空');
            return;
        }

        Logger.debug(`处理用户消息: ${content.substring(0, 50)}...`);

        try {
            // 发送消息并获取 AI 响应
            const response = await this.chatService.sendMessage(content);
            
            // 将 AI 响应发送到 Webview
            this.sendMessageToWebview(response);
            
        } catch (error) {
            Logger.error('发送消息失败', error as Error);
            this.sendErrorToWebview('发送消息失败，请重试');
        }
    }

    /**
     * 处理清空聊天
     */
    private async handleClearChat(): Promise<void> {
        Logger.debug('处理清空聊天请求');
        
        try {
            this.chatService.clearCurrentSession();
            this.sendToWebview({
                type: 'clearMessages'
            });
            Logger.success('聊天已清空');
        } catch (error) {
            Logger.error('清空聊天失败', error as Error);
            this.sendErrorToWebview('清空聊天失败');
        }
    }

    /**
     * 处理导出聊天
     */
    private async handleExportChat(): Promise<void> {
        Logger.debug('处理导出聊天请求');
        
        try {
            // 触发导出命令
            await vscode.commands.executeCommand('ai-chat.exportChat');
        } catch (error) {
            Logger.error('导出聊天失败', error as Error);
            this.sendErrorToWebview('导出聊天失败');
        }
    }

    /**
     * 处理加载历史
     */
    private async handleLoadHistory(): Promise<void> {
        Logger.debug('处理加载历史请求');
        
        try {
            // 触发历史命令
            await vscode.commands.executeCommand('ai-chat.showHistory');
        } catch (error) {
            Logger.error('加载历史失败', error as Error);
            this.sendErrorToWebview('加载历史失败');
        }
    }

    /**
     * 处理更新配置
     * @param config 新配置
     */
    private async handleUpdateConfig(config: any): Promise<void> {
        Logger.debug('处理更新配置请求', config);
        
        try {
            // 这里可以添加配置更新逻辑
            Logger.info('配置已更新', config);
        } catch (error) {
            Logger.error('更新配置失败', error as Error);
            this.sendErrorToWebview('更新配置失败');
        }
    }

    /**
     * 发送消息到 Webview
     * @param message 聊天消息
     */
    private sendMessageToWebview(message: any): void {
        if (!this._view) {
            Logger.warn('Webview 未初始化，无法发送消息');
            return;
        }

        this.sendToWebview({
            type: 'addMessage',
            data: {
                type: message.type,
                content: message.content,
                timestamp: message.timestamp.toLocaleTimeString()
            }
        });
    }

    /**
     * 发送错误消息到 Webview
     * @param errorMessage 错误消息
     */
    private sendErrorToWebview(errorMessage: string): void {
        this.sendToWebview({
            type: 'error',
            data: {
                content: errorMessage
            }
        });
    }

    /**
     * 发送消息到 Webview
     * @param message 消息对象
     */
    private sendToWebview(message: any): void {
        if (!this._view) {
            Logger.warn('Webview 未初始化，无法发送消息');
            return;
        }

        this._view.webview.postMessage(message);
    }

    /**
     * 刷新当前会话
     */
    public refreshCurrentSession(): void {
        Logger.methodCall('ChatViewProvider', 'refreshCurrentSession');
        
        if (!this._view) {
            return;
        }

        // 清空当前显示的消息
        this.sendToWebview({
            type: 'clearMessages'
        });

        // 重新加载当前会话的消息
        const session = this.chatService.getCurrentSession();
        for (const message of session.messages) {
            this.sendMessageToWebview(message);
        }
    }

    /**
     * 显示欢迎消息
     */
    public showWelcomeMessage(): void {
        if (!this._view) {
            return;
        }

        this.sendToWebview({
            type: 'clearMessages'
        });
    }

    /**
     * 获取当前视图状态
     * @returns 视图是否可见
     */
    public isVisible(): boolean {
        return this._view?.visible ?? false;
    }

    /**
     * 聚焦到视图
     */
    public focus(): void {
        if (this._view) {
            this._view.show?.(true);
        }
    }
}
